import 'package:example/view_model/services/auth_manager/social_auth_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:google_sign_in/google_sign_in.dart';

import '../../resources/exports/index.dart';
import '../../view_model/controllers/merchant_login_controller/merchant_login_controller.dart';

class MerchantLogin extends GetView<MerchantLoginController> {
  const MerchantLogin({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      appBar: const CustomAppBar(title: Strings.LOGIN, backallow: false),
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 24),
        child: GetBuilder<MerchantLoginController>(
          id: 'page',
          builder: (_) {
            if (controller.isLoading.value) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            } else {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SpaceH20(),
                  Text(Strings.ENTER_YOUR_USER_NAME, style: context.headlineSmall),
                  const SpaceH20(),
                  CustomTextFormField(
                    controller: controller.userNameCtl,
                    isRequired: true,
                    height: Sizes.HEIGHT_20,
                    labelText: Strings.USER_NAME,
                    labelColor: AppColors.black,
                    prefixIcon: Icons.person,
                    prefixIconColor: AppColors.black,
                    textColor: AppColors.black,
                    cursorColor: AppColors.black,
                    errorColor: AppColors.black,
                    enableBorderColor: AppColors.black,
                    focusBorderColor: AppColors.primary,
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                  ),
                  const SpaceH20(),
                  CustomTextFormField(
                    controller: controller.passwordCtl,
                    isRequired: true,
                    height: Sizes.HEIGHT_20,
                    labelText: Strings.password,
                    labelColor: AppColors.black,
                    prefixIcon: Icons.password,
                    prefixIconColor: AppColors.black,
                    textColor: AppColors.black,
                    cursorColor: AppColors.black,
                    errorColor: AppColors.black,
                    enableBorderColor: AppColors.black,
                    focusBorderColor: AppColors.primary,
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                  ),
                  const SpaceH20(),
                  CustomButton.solid(
                    backgroundColor: AppColors.primary,
                    textColor: AppColors.white,
                    text: Strings.LOGIN,
                    onTapAsync: () async => controller.merchantLogin(true),
                    radius: Sizes.RADIUS_12,
                    constraints: const BoxConstraints(minHeight: 55),
                  ),
                  const SpaceH20(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "Don't have an account? ",
                        style: context.bodyMedium,
                      ),
                      TextButton(
                        onPressed: () => Get.toNamed(Routes.CHOOSE_AUTHENTICATION_METHOD),
                        child: Text(
                          'Sign Up',
                          style: context.bodyMedium.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }
}

class ChosseAuthenticationMethod extends StatefulWidget {
  const ChosseAuthenticationMethod({super.key});

  @override
  State<ChosseAuthenticationMethod> createState() => ChosseAuthenticationMethodState();
}

class ChosseAuthenticationMethodState extends State<ChosseAuthenticationMethod> {
  EnumDocumentTypeSelection _selectedDocumentType = EnumDocumentTypeSelection.non;

  void _selectDocumentType(EnumDocumentTypeSelection documentType) {
    setState(() {
      _selectedDocumentType = documentType;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      appBar: AppBar(
        title: const Text("Choose Verification Method"),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            const SizedBox(),
            // National ID Button
            Column(
              children: [
                Text(
                  "Please select the authentication method you want to use",
                  style: context.titleLarge,
                  textAlign: TextAlign.center,
                ),
                const SpaceH24(),
                // National ID Button)
                _buildDocumentTypeButton(
                  context: context,
                  title: "Contine with Email/Phone",
                  documentType: EnumDocumentTypeSelection.emailOrPhone,
                  onPressed: () => _selectDocumentType(EnumDocumentTypeSelection.emailOrPhone),
                ),
                const SpaceH16(),
                // Passport Button
                _buildDocumentTypeButton(
                  context: context,
                  title: "Google Auth",
                  documentType: EnumDocumentTypeSelection.google,
                  onPressed: () => _selectDocumentType(EnumDocumentTypeSelection.google),
                ),
                const SpaceH16(),
                _buildDocumentTypeButton(
                  context: context,
                  title: "Apple Auth",
                  documentType: EnumDocumentTypeSelection.apple,
                  onPressed: () => _selectDocumentType(EnumDocumentTypeSelection.apple),
                ),
              ],
            ),

            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: _selectedDocumentType != EnumDocumentTypeSelection.non
                  ? () async {
                      if (_selectedDocumentType == EnumDocumentTypeSelection.emailOrPhone) {
                        Get.toNamed(Routes.SIGNUP);
                      } else if (_selectedDocumentType == EnumDocumentTypeSelection.google) {
                        UserCredential userCredential = await SocialAuthService().signInWithGoogle();
                        print(userCredential.user?.email);
                        print(userCredential.user?.displayName);
                        print(userCredential.user?.photoURL);
                        print(userCredential.user?.phoneNumber);
                      } else if (_selectedDocumentType == EnumDocumentTypeSelection.apple) {
                        UserCredential userCredential = await SocialAuthService().signInWithApple();
                        print(userCredential.user?.email);
                        print(userCredential.user?.displayName);
                        print(userCredential.user?.photoURL);
                        print(userCredential.user?.phoneNumber);
                      }
                    }
                  : null,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 14),
                width: double.maxFinite,
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  color: _selectedDocumentType != EnumDocumentTypeSelection.non ? const Color(0xff8240DE) : const Color(0xff8240DE).withOpacity(0.5),
                  shape: ContinuousRectangleBorder(
                    borderRadius: BorderRadius.circular(24.0),
                  ),
                ),
                alignment: Alignment.center,
                child: DefaultTextStyle(
                  style: TextStyle(
                    color: _selectedDocumentType != EnumDocumentTypeSelection.non ? Colors.white : Colors.white.withOpacity(0.7),
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  child: const Text("Proceed"),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentTypeButton({
    required BuildContext context,
    required String title,
    required EnumDocumentTypeSelection documentType,
    required VoidCallback onPressed,
  }) {
    bool isSelected = _selectedDocumentType == documentType;

    return CupertinoButton(
      onPressed: onPressed,
      padding: EdgeInsets.zero,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: ShapeDecoration(
          color: isSelected ? const Color(0xff8240DE).withOpacity(0.1) : const Color(0xffE8E0EB),
          shape: ContinuousRectangleBorder(
            borderRadius: BorderRadius.circular(24.0),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Radio<EnumDocumentTypeSelection>(
                value: documentType,
                groupValue: _selectedDocumentType,
                onChanged: (EnumDocumentTypeSelection? value) {
                  if (value != null) {
                    _selectDocumentType(value);
                  }
                },
                activeColor: const Color(0xff8240DE),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: context.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                  color: isSelected ? const Color(0xff8240DE) : Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

enum EnumDocumentTypeSelection { non, emailOrPhone, apple, google }
