import '../../resources/exports/index.dart';

class Signup extends GetView<SignupController> {
  const Signup({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      appBar: const CustomAppBar(title: "Sign Up", backallow: true),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
          child: Form(
            key: controller.formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Create your account',
                  style: context.headlineLarge.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SpaceH4(),
                Text(
                  'Please fill in the details below to create your account',
                  style: context.titleMedium.copyWith(
                    color: AppColors.greyShade2,
                  ),
                ),
                const SpaceH32(),

                // Username Field
                CustomTextFormField(
                  controller: controller.usernameCtrl,
                  isRequired: true,
                  height: Sizes.HEIGHT_20,
                  labelText: Strings.USER_NAME,
                  labelColor: AppColors.black,
                  prefixIcon: EneftyIcons.user_outline,
                  prefixIconColor: AppColors.black,
                  textColor: AppColors.black,
                  cursorColor: AppColors.black,
                  errorColor: AppColors.error,
                  enableBorderColor: AppColors.black,
                  focusBorderColor: AppColors.primary,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.text,
                  validator: controller.validateUsername,
                ),
                const SpaceH20(),

                // Email/Phone Field with Toggle
                Obx(() => controller.isEmailType.value ? _buildEmailField() : _buildPhoneField()),
                const SpaceH8(),

                // Toggle Button
                Align(
                  alignment: Alignment.centerRight,
                  child: Obx(() => TextButton(
                        onPressed: controller.toggleInputType,
                        child: Text(
                          controller.isEmailType.value ? 'Use phone number instead' : 'Use email instead',
                          style: context.bodyMedium.copyWith(
                            color: AppColors.primary,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      )),
                ),
                const SpaceH20(),

                // Password Field
                CustomTextFormField(
                  controller: controller.passwordCtrl,
                  isRequired: true,
                  height: Sizes.HEIGHT_20,
                  labelText: Strings.password,
                  labelColor: AppColors.black,
                  prefixIcon: EneftyIcons.lock_outline,
                  prefixIconColor: AppColors.black,
                  textColor: AppColors.black,
                  cursorColor: AppColors.black,
                  errorColor: AppColors.error,
                  enableBorderColor: AppColors.black,
                  focusBorderColor: AppColors.primary,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.visiblePassword,
                  obscureText: true,
                  validator: controller.validatePassword,
                ),
                const SpaceH20(),

                // Confirm Password Field
                CustomTextFormField(
                  controller: controller.confirmPasswordCtrl,
                  isRequired: true,
                  height: Sizes.HEIGHT_20,
                  labelText: 'Confirm Password',
                  labelColor: AppColors.black,
                  prefixIcon: EneftyIcons.lock_outline,
                  prefixIconColor: AppColors.black,
                  textColor: AppColors.black,
                  cursorColor: AppColors.black,
                  errorColor: AppColors.error,
                  enableBorderColor: AppColors.black,
                  focusBorderColor: AppColors.primary,
                  textInputAction: TextInputAction.done,
                  keyboardType: TextInputType.visiblePassword,
                  obscureText: true,
                  validator: controller.validateConfirmPassword,
                ),
                const SpaceH32(),

                // Signup Button
                GetBuilder<SignupController>(
                  id: 'signup_button',
                  builder: (_) {
                    return CustomButton.solid(
                      backgroundColor: AppColors.primary,
                      textColor: AppColors.white,
                      text: controller.isLoading.value ? 'Creating Account...' : 'Sign Up',
                      onTapAsync: controller.isLoading.value ? null : () async => controller.signup(),
                      radius: Sizes.RADIUS_12,
                      constraints: const BoxConstraints(minHeight: 55),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmailField() {
    return CustomTextFormField(
      controller: controller.emailCtrl,
      isRequired: true,
      height: Sizes.HEIGHT_20,
      labelText: Strings.EMAIL,
      labelColor: AppColors.black,
      prefixIcon: EneftyIcons.sms_outline,
      prefixIconColor: AppColors.black,
      textColor: AppColors.black,
      cursorColor: AppColors.black,
      errorColor: AppColors.error,
      enableBorderColor: AppColors.black,
      focusBorderColor: AppColors.primary,
      textInputAction: TextInputAction.next,
      keyboardType: TextInputType.emailAddress,
      autofillHints: const [AutofillHints.email],
      validator: (value) => controller.isEmailType.value ? Validators.emailValidation(value) : null,
    );
  }

  Widget _buildPhoneField() {
    return IntlPhoneField(
      decoration: const InputDecoration(
        labelText: Strings.PHONE,
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(12.0)),
          borderSide: BorderSide(color: AppColors.black),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(12.0)),
          borderSide: BorderSide(color: AppColors.primary),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(12.0)),
          borderSide: BorderSide(color: AppColors.error),
        ),
      ),
      autovalidateMode: AutovalidateMode.disabled,
      controller: controller.phoneCtrl,
      keyboardType: TextInputType.number,
      initialCountryCode: 'IQ',
      languageCode: "en",
      onChanged: (phone) {},
      onCountryChanged: (country) => controller.countryCode = country,
      inputFormatters: InputFormat.onlyNumber,
      validator: (phone) => !controller.isEmailType.value && (phone?.number.isEmpty ?? true) ? 'Phone number is required' : null,
    );
  }
}
