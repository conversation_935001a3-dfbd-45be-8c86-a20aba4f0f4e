import 'package:digital_onboarding/digital_onboarding.dart';
import 'package:intl_phone_field/countries.dart';
import '../../../resources/exports/index.dart';

enum LoginType { email, phone }

class LoginController extends GetxController with GetSingleTickerProviderStateMixin {
  late TabController tabCtrl;
  late TextEditingController emailCtrl;
  late TextEditingController phoneCtrl;

  Country? countryCode;

  Future<void> sendOtp(LoginType type) async {
    try {
      DigitalOnBoardingLoginType loginType = type.name == "phone" ? DigitalOnBoardingLoginType.phone : DigitalOnBoardingLoginType.email;
      String value = type.name == "phone" ? "${countryCode?.dialCode ?? "964"}${phoneCtrl.text}" : emailCtrl.text;

      print(value);

      bool? otpSent = await DigitalOnboardingServices.sendOtp(loginType: loginType, value: value);

      if (!otpSent) {
        CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
        return;
      }

      Get.toNamed(Routes.OTP, arguments: {'otpModel': SessionOtpModel(email: (emailCtrl.text == "") ? null : emailCtrl.text, phone: "${countryCode?.dialCode ?? "964"}${phoneCtrl.text}")});
    } on DigitalOnboardingException catch (e) {
      inspect(e);
    }
  }

  @override
  void onInit() {
    tabCtrl = TabController(length: 2, vsync: this);
    emailCtrl = TextEditingController();
    phoneCtrl = TextEditingController();
    super.onInit();
  }
}
